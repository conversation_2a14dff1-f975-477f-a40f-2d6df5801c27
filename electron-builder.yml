appId: com.clouddrive.app
productName: Cloud Drive

directories:
  output: release

extraResources:
  - from: .env.development
    to: .env.development
  - from: .env.production
    to: .env.production
  - from: .env.test
    to: .env.test

files:
  - dist/**/*
  - dist-electron/**/*

# 确保7zip-bin被正确打包
asarUnpack:
  - "**/node_modules/7zip-bin/**/*"

# Windows配置
win:
  target:
    target: nsis
    arch:
      - x64

# macOS配置
mac:
  target: dmg

# NSIS安装程序配置
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
